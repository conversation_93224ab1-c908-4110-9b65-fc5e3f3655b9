# 用药方法按钮状态恢复修复

## 问题描述

在用药界面中，当用法用量中有用药方法选项（内服/外用按钮）时，用户选择后进入生成医案界面，点击发送给患者。发送成功后，从聊天界面切换到用药界面，此时之前选择的用药方法按钮状态没有恢复状态。

## 问题分析

1. **根本原因**：用药方法选择（内服/外用按钮）的状态没有在缓存恢复时正确设置
2. **数据流程**：
   - 用药方法会被保存到 `OrderMsgBean` 的 `mode` 字段中
   - 在 `setMedicationParams()` 方法中，用药方法会被正确保存
   - 但在 `showCachePresMsg()` 方法中，没有恢复用药方法按钮的选中状态
   - 在 `setDefaultViewAndData()` 方法中，也没有重置用药方法按钮状态

## 修复方案

### 1. 在缓存恢复时恢复按钮状态

在 `showCachePresMsg()` 方法中添加用药方法按钮状态的恢复逻辑：

```java
// 恢复用药方法按钮状态
if (!TextUtils.isEmpty(medicationParams.getMode())) {
    if ("内服".equals(medicationParams.getMode())) {
        btnInternalUse.setSelected(true);
        btnExternalUse.setSelected(false);
    } else if ("外用".equals(medicationParams.getMode())) {
        btnInternalUse.setSelected(false);
        btnExternalUse.setSelected(true);
    }
} else {
    // 如果没有缓存的用药方法，重置按钮状态
    btnInternalUse.setSelected(false);
    btnExternalUse.setSelected(false);
}
```

### 2. 在清空数据时重置按钮状态

在 `setDefaultViewAndData()` 方法中添加用药方法按钮状态的重置逻辑：

```java
// 重置用药方法按钮状态
btnInternalUse.setSelected(false);
btnExternalUse.setSelected(false);
```

## 修改的文件

- `app/src/main/java/com/doctor/br/fragment/chatmain/MedicationFragment.java`

## 修改的方法

1. `showCachePresMsg()` - 添加用药方法按钮状态恢复逻辑
2. `setDefaultViewAndData()` - 添加用药方法按钮状态重置逻辑

## 测试验证

修复后需要验证以下场景：

1. **缓存恢复场景**：
   - 选择用药方法（内服或外用）
   - 进入生成医案界面并发送给患者
   - 从聊天界面切换回用药界面
   - 验证用药方法按钮状态是否正确恢复

2. **数据清空场景**：
   - 选择用药方法后切换患者
   - 验证用药方法按钮状态是否被正确重置

3. **不同剂型场景**：
   - 验证只有在显示用药方法选项的剂型（饮片、代煎、散剂）中，按钮状态才会被恢复
   - 验证其他剂型不受影响

## 相关代码逻辑

### 用药方法显示控制

用药方法选项只在特定剂型中显示：

```java
//单独进行处理 饮片、代煎、散剂
if (PublicParams.DOSAGEFORM_SLICES.equals(mDrugForm) ||
        PublicParams.DOSAGEFORM_REPLACE_DECOCTION.equals(mDrugForm) ||
        PublicParams.DOSAGEFORM_POWDER.equals(mDrugForm)){
    usageMethodLayout.setVisibility(View.VISIBLE);
}else {
    usageMethodLayout.setVisibility(View.GONE);
}
```

### 用药方法保存逻辑

在提交时，用药方法会被保存：

```java
//服药方式 只有显示的时候才进行赋值
if (usageMethodLayout.getVisibility() == View.VISIBLE) {
    medicationParams.setMode(btnInternalUse.isSelected() ?
            btnInternalUse.getText().toString() :
            btnExternalUse.getText().toString()
    );
}
```

## 总结

此次修复解决了用药方法按钮状态在页面切换后无法正确恢复的问题，确保了用户体验的一致性。修复涵盖了缓存恢复和数据清空两个关键场景，保证了按钮状态的正确管理。
